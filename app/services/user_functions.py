"""
gRPC Authentication Service Implementation

This module implements the gRPC service for OAuth authentication,
credential management, and dynamic integration management.
"""

import asyncio
import logging

import grpc
import json
import uuid
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from sqlalchemy import text
from app.core.oauth_providers import OAuthProvider
from app.db.session import get_db
from app.grpc_ import authentication_pb2, authentication_pb2_grpc

# from app.services.oauth_service import oauth_service
from app.utils.integrations_helpers import oauth_service
from app.models.integrations import IntegrationDefinition, ConnectionTypeEnum, OAuthCredential
from app.db.session import get_db


logger = logging.getLogger(__name__)


class UserIntegrationsService(authentication_pb2_grpc.AuthenticationServiceServicer):
    """gRPC Authentication Service implementation."""

    def __init__(self):
        """Initialize the authentication servicer."""
        self.oauth_service = oauth_service

    def _get_db_session(self) -> Session:
        """Get database session."""
        try:
            session = next(get_db())
            logger.debug("Database session created successfully")
            return session
        except Exception as e:
            logger.error(f"Failed to create database session: {e}")
            raise
        finally:
            session.close()
            logger.debug("Database session closed")

    def ListUserIntegrations(
        self, request: authentication_pb2.ListUserIntegrationsRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.ListUserIntegrationsResponse:
        """List user's connected integrations."""
        try:
            if not request.user_id:
                return authentication_pb2.ListUserIntegrationsResponse(
                    success=False, message="User ID is required", integrations=[]
                )

            db = self._get_db_session()
            try:
                # Query user's OAuth credentials with integration definitions
                query = (
                    db.query(OAuthCredential, IntegrationDefinition)
                    .join(
                        IntegrationDefinition,
                        OAuthCredential.integration_definition_id == IntegrationDefinition.id,
                    )
                    .filter(
                        OAuthCredential.user_id == request.user_id,
                        IntegrationDefinition.is_active == True,
                    )
                )

                # Filter by connection type if specified
                if request.connection_type_filter != authentication_pb2.CONNECTION_TYPE_UNSPECIFIED:
                    connection_type_map = {
                        authentication_pb2.CONNECTION_TYPE_API_KEY: ConnectionTypeEnum.API_KEY,
                        authentication_pb2.CONNECTION_TYPE_OAUTH: ConnectionTypeEnum.OAUTH,
                    }
                    connection_type = connection_type_map.get(request.connection_type_filter)
                    if connection_type:
                        query = query.filter(
                            IntegrationDefinition.connection_type == connection_type
                        )

                results = query.order_by(IntegrationDefinition.name).all()

                # Convert to proto messages
                user_integrations = []
                for credential, integration in results:
                    scopes = []
                    if credential.scopes:
                        try:
                            scopes = json.loads(credential.scopes)
                        except json.JSONDecodeError:
                            scopes = []

                    user_integration = authentication_pb2.UserIntegrationStatus(
                        user_id=credential.user_id,
                        integration_id=integration.id,
                        integration_name=integration.name,
                        is_connected=credential.is_connected,
                        last_used_at=(
                            credential.last_used_at.isoformat() if credential.last_used_at else ""
                        ),
                        created_at=(
                            credential.created_at.isoformat() if credential.created_at else ""
                        ),
                        scopes=scopes,
                    )
                    user_integrations.append(user_integration)

                return authentication_pb2.ListUserIntegrationsResponse(
                    success=True,
                    message=f"Retrieved {len(user_integrations)} user integration(s)",
                    integrations=user_integrations,
                )

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error listing user integrations: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.ListUserIntegrationsResponse(
                success=False, message="Internal server error", integrations=[]
            )

    def InitiateOAuthByIntegration(
        self,
        request: authentication_pb2.InitiateOAuthByIntegrationRequest,
        context: grpc.ServicerContext,
    ) -> authentication_pb2.InitiateOAuthByIntegrationResponse:
        """Initiate OAuth authorization flow for a specific integration."""
        try:
            # Validate required fields
            if not request.user_id:
                return authentication_pb2.InitiateOAuthByIntegrationResponse(
                    success=False, message="User ID is required"
                )

            if not request.integration_id:
                return authentication_pb2.InitiateOAuthByIntegrationResponse(
                    success=False, message="Integration ID is required"
                )

            db = self._get_db_session()
            try:
                auth_url, state_token = (
                    self.oauth_service.generate_authorization_url_for_integration(
                        db=db,
                        user_id=request.user_id,
                        integration_id=request.integration_id,
                        custom_redirect_uri=request.redirect_uri if request.redirect_uri else None,
                    )
                )

                print(f"[DEBUG] auth_url: {auth_url}")
                print(f"[DEBUG] state_token: {state_token}")

                return authentication_pb2.InitiateOAuthByIntegrationResponse(
                    success=True,
                    message="OAuth authorization URL generated successfully",
                    authorization_url=auth_url,
                    state=state_token,
                )

            finally:
                db.close()

        except ValueError as e:
            logger.error(f"OAuth initiation error for integration: {e}")
            return authentication_pb2.InitiateOAuthByIntegrationResponse(
                success=False, message=str(e)
            )
        except Exception as e:
            logger.error(f"Unexpected error in OAuth initiation for integration: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.InitiateOAuthByIntegrationResponse(
                success=False, message="Internal server error"
            )

    def GetOAuthCredentialsByIntegration(
        self,
        request: authentication_pb2.GetOAuthCredentialsByIntegrationRequest,
        context: grpc.ServicerContext,
    ) -> authentication_pb2.GetOAuthCredentialsByIntegrationResponse:
        """Retrieve OAuth credentials for a specific integration."""
        try:
            if not request.user_id:
                return authentication_pb2.GetOAuthCredentialsByIntegrationResponse(
                    success=False, message="User ID is required"
                )

            if not request.integration_id:
                return authentication_pb2.GetOAuthCredentialsByIntegrationResponse(
                    success=False, message="Integration ID is required"
                )

            db = self._get_db_session()
            try:
                credentials = self.oauth_service.retrieve_oauth_credentials_for_integration(
                    db=db,
                    user_id=request.user_id,
                    integration_id=request.integration_id,
                )

                if credentials:
                    # Ensure expires_in is an integer
                    expires_in = credentials.get("expires_in")
                    if expires_in is None:
                        expires_in = 0
                    elif isinstance(expires_in, str):
                        try:
                            expires_in = int(expires_in)
                        except (ValueError, TypeError):
                            expires_in = 0
                    elif not isinstance(expires_in, int):
                        expires_in = 0

                    return authentication_pb2.GetOAuthCredentialsByIntegrationResponse(
                        success=True,
                        message="OAuth credentials retrieved successfully",
                        user_id=credentials["user_id"],
                        integration_id=request.integration_id,
                        access_token=credentials.get("access_token", ""),
                        refresh_token=credentials.get("refresh_token", ""),
                        token_type=credentials.get("token_type", "Bearer"),
                        expires_in=expires_in,
                        scope=credentials.get("scope", ""),
                    )
                else:
                    return authentication_pb2.GetOAuthCredentialsByIntegrationResponse(
                        success=False, message="OAuth credentials not found"
                    )
            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error retrieving OAuth credentials for integration: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.GetOAuthCredentialsByIntegrationResponse(
                success=False, message="Internal server error"
            )

    def DeleteOAuthCredentialsByIntegration(
        self,
        request: authentication_pb2.DeleteOAuthCredentialsByIntegrationRequest,
        context: grpc.ServicerContext,
    ) -> authentication_pb2.DeleteOAuthCredentialsByIntegrationResponse:
        """Delete OAuth credentials for a specific integration."""
        try:
            if not request.user_id:
                return authentication_pb2.DeleteOAuthCredentialsByIntegrationResponse(
                    success=False, message="User ID is required"
                )

            if not request.integration_id:
                return authentication_pb2.DeleteOAuthCredentialsByIntegrationResponse(
                    success=False, message="Integration ID is required"
                )

            db = self._get_db_session()
            try:
                success = self.oauth_service.delete_oauth_credentials_for_integration(
                    db=db,
                    user_id=request.user_id,
                    integration_id=request.integration_id,
                )

                if success:
                    return authentication_pb2.DeleteOAuthCredentialsByIntegrationResponse(
                        success=True, message="OAuth credentials deleted successfully"
                    )
                else:
                    return authentication_pb2.DeleteOAuthCredentialsByIntegrationResponse(
                        success=False,
                        message="Failed to delete OAuth credentials or credentials not found",
                    )

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error deleting OAuth credentials for integration: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.DeleteOAuthCredentialsByIntegrationResponse(
                success=False, message="Internal server error"
            )

    def HandleOAuthCallback(
        self, request: authentication_pb2.OAuthCallbackRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.OAuthCallbackResponse:
        """Handle OAuth callback and exchange code for tokens."""
        logger.info(
            f"[DEBUG] HandleOAuthCallback - Request received: code: {request.code[:20] if request.code else 'None'}..., state: {request.state[:20] if request.state else 'None'}..."
        )
        try:
            print(f"[DEBUG] HandleOAuthCallback - Request: {request}")
            # Try to get state_data for redirect_url even in error cases
            redirect_url = ""
            if request.state:
                try:
                    # Get state data from Redis directly
                    from app.utils.redis_service_new import redis_service
                    import json

                    redis_key = f"oauth_state:{request.state}"
                    state_data_raw = redis_service.get_data_from_redis(redis_key)
                    if state_data_raw:
                        state_data = json.loads(state_data_raw)
                        redirect_url = state_data.get("custom_redirect_uri", "")
                except Exception:
                    # If we can't get state_data, continue without redirect_url
                    pass

            # Handle OAuth errors
            if request.error:
                logger.error(f"OAuth error received in callback: {request.error}")
                return authentication_pb2.OAuthCallbackResponse(
                    success=False,
                    message=f"Authorization failed: {request.error}",
                    redirect_url=redirect_url,
                )

            if not request.code:
                return authentication_pb2.OAuthCallbackResponse(
                    success=False,
                    message="Authorization code not provided",
                    redirect_url=redirect_url,
                )

            # Exchange code for tokens
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            db = self._get_db_session()
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                # Determine flow type (dynamic vs. legacy)
                redis_key = f"oauth_state:{request.state}"
                state_data_raw = redis_service.get_data_from_redis(redis_key)
                state_data = json.loads(state_data_raw) if state_data_raw else {}

                logger.info(f"[DEBUG] HandleOAuthCallback - State data retrieved: {state_data}")
                logger.info(
                    f"[DEBUG] HandleOAuthCallback - Checking for integration_id: {'integration_id' in state_data}"
                )

                if "integration_id" in state_data:
                    # New dynamic flow
                    logger.info(
                        f"[DEBUG] HandleOAuthCallback - Using dynamic integration flow for integration_id: {state_data.get('integration_id')}"
                    )
                    tokens, final_state_data = loop.run_until_complete(
                        self.oauth_service.exchange_code_for_tokens_for_integration(
                            db=db, code=request.code, state=request.state
                        )
                    )

                    # Get scopes from integration definition schema, not from state data
                    integration_def = (
                        db.query(IntegrationDefinition)
                        .filter(IntegrationDefinition.id == final_state_data["integration_id"])
                        .first()
                    )
                    scopes = []
                    if integration_def and integration_def.schema_definition:
                        scopes = integration_def.schema_definition.get("scopes", [])

                    result = self.oauth_service.store_oauth_credentials_for_integration(
                        db=db,
                        user_id=final_state_data["user_id"],
                        integration_id=final_state_data["integration_id"],
                        tokens=tokens,
                        scopes=scopes,
                    )
                    user_id = final_state_data["user_id"]
                    tool_name = str(
                        final_state_data["integration_id"]
                    )  # Use integration_id as tool_name for now
                    provider = "integration"
                    redirect_url = final_state_data.get("custom_redirect_uri", "")

                print(f"[DEBUG] {tool_name}")
                if result["success"]:
                    return authentication_pb2.OAuthCallbackResponse(
                        success=True,
                        message="OAuth authorization completed successfully",
                        user_id=user_id,
                        tool_name=tool_name,
                        provider=provider,
                        redirect_url=redirect_url,
                    )
                else:
                    return authentication_pb2.OAuthCallbackResponse(
                        success=False,
                        message=f"Failed to store credentials: {result.get('message', 'Unknown error')}",
                        redirect_url=redirect_url,
                    )

            finally:
                if "loop" in locals() and loop.is_running():
                    loop.close()
                db.close()

        except ValueError as e:
            logger.error(f"OAuth callback error: {e}")
            return authentication_pb2.OAuthCallbackResponse(
                success=False, message=str(e), redirect_url=""
            )
        except Exception as e:
            logger.error(f"Unexpected error in OAuth callback: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.OAuthCallbackResponse(
                success=False, message="Internal server error", redirect_url=""
            )
