"""
gRPC Authentication Service Implementation

This module implements the gRPC service for OAuth authentication,
credential management, and dynamic integration management.
"""

import asyncio
import logging

import grpc
import json
import uuid
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.session import get_db
from app.grpc_ import authentication_pb2, authentication_pb2_grpc

from app.models.integrations import IntegrationDefinition, ConnectionTypeEnum, OAuthCredential
from app.db.session import get_db


logger = logging.getLogger(__name__)


class IntegrationService(authentication_pb2_grpc.AuthenticationServiceServicer):
    """gRPC Authentication Service implementation."""

    def _get_db_session(self) -> Session:
        """Get database session."""
        try:
            session = next(get_db())
            logger.debug("Database session created successfully")
            return session
        except Exception as e:
            logger.error(f"Failed to create database session: {e}")
            raise
        finally:
            session.close()
            logger.debug("Database session closed")

    def CreateIntegration(
        self, request: authentication_pb2.CreateIntegrationRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.CreateIntegrationResponse:
        """Create a new integration definition."""
        try:
            print(f"[DEBUG] CreateIntegration request: {request}")

            # Validate required fields
            if not request.admin_user_id:
                return authentication_pb2.CreateIntegrationResponse(
                    success=False, message="Admin user ID is required"
                )

            if not request.name or not request.name.strip():
                return authentication_pb2.CreateIntegrationResponse(
                    success=False, message="Integration name is required"
                )

            if not request.schema_definition or not request.schema_definition.strip():
                return authentication_pb2.CreateIntegrationResponse(
                    success=False, message="Schema definition is required"
                )

            # Validate schema definition is valid JSON
            try:
                schema_dict = json.loads(request.schema_definition)
            except json.JSONDecodeError:
                return authentication_pb2.CreateIntegrationResponse(
                    success=False, message="Schema definition must be valid JSON"
                )

            # Map connection type from proto to enum
            connection_type_map = {
                authentication_pb2.CONNECTION_TYPE_API_KEY: ConnectionTypeEnum.API_KEY,
                authentication_pb2.CONNECTION_TYPE_OAUTH: ConnectionTypeEnum.OAUTH,
            }
            connection_type = connection_type_map.get(request.connection_type)
            if not connection_type:
                return authentication_pb2.CreateIntegrationResponse(
                    success=False, message="Invalid connection type"
                )

            db = self._get_db_session()
            try:
                # Check if integration with same name already exists
                existing = (
                    db.query(IntegrationDefinition)
                    .filter(IntegrationDefinition.name == request.name.strip())
                    .first()
                )

                if existing:
                    return authentication_pb2.CreateIntegrationResponse(
                        success=False,
                        message=f"Integration with name '{request.name}' already exists",
                    )

                # Create new integration
                new_integration = IntegrationDefinition(
                    id=str(uuid.uuid4()),
                    logo=request.logo if request.logo else None,
                    name=request.name.strip(),
                    description=request.description if request.description else None,
                    connection_type=connection_type,
                    schema_definition=schema_dict,
                    is_active=True,
                    created_at=datetime.now(timezone.utc),
                    updated_at=datetime.now(timezone.utc),
                )

                print(f"[DEBUG] new_integration: {new_integration}")
                db.add(new_integration)
                db.commit()

                # Convert to proto message
                integration_proto = self._integration_to_proto(new_integration)

                logger.info(
                    f"Created new integration: {new_integration.name} (ID: {new_integration.id})"
                )

                return authentication_pb2.CreateIntegrationResponse(
                    success=True,
                    message="Integration created successfully",
                    integration=integration_proto,
                )

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error creating integration: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.CreateIntegrationResponse(
                success=False, message="Internal server error"
            )

    def UpdateIntegration(
        self, request: authentication_pb2.UpdateIntegrationRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.UpdateIntegrationResponse:
        """Update an existing integration definition."""
        try:

            if not request.admin_user_id:
                return authentication_pb2.UpdateIntegrationResponse(
                    success=False, message="Admin user ID is required"
                )

            if not request.integration_id:
                return authentication_pb2.UpdateIntegrationResponse(
                    success=False, message="Integration ID is required"
                )

            db = self._get_db_session()
            try:
                # Find existing integration
                integration = (
                    db.query(IntegrationDefinition)
                    .filter(IntegrationDefinition.id == request.integration_id)
                    .first()
                )

                if not integration:
                    return authentication_pb2.UpdateIntegrationResponse(
                        success=False, message="Integration not found"
                    )

                # Update fields if provided
                if request.logo:
                    integration.logo = request.logo
                if request.name and request.name.strip():
                    # Check if name conflicts with another integration
                    existing_with_name = (
                        db.query(IntegrationDefinition)
                        .filter(
                            IntegrationDefinition.name == request.name.strip(),
                            IntegrationDefinition.id != request.integration_id,
                        )
                        .first()
                    )
                    if existing_with_name:
                        return authentication_pb2.UpdateIntegrationResponse(
                            success=False,
                            message=f"Integration with name '{request.name}' already exists",
                        )
                    integration.name = request.name.strip()

                if request.description:
                    integration.description = request.description

                if request.connection_type != authentication_pb2.CONNECTION_TYPE_UNSPECIFIED:
                    connection_type_map = {
                        authentication_pb2.CONNECTION_TYPE_API_KEY: ConnectionTypeEnum.API_KEY,
                        authentication_pb2.CONNECTION_TYPE_OAUTH: ConnectionTypeEnum.OAUTH,
                    }
                    connection_type = connection_type_map.get(request.connection_type)
                    if connection_type:
                        integration.connection_type = connection_type

                if request.schema_definition and request.schema_definition.strip():
                    try:
                        schema_dict = json.loads(request.schema_definition)
                        integration.schema_definition = schema_dict
                    except json.JSONDecodeError:
                        return authentication_pb2.UpdateIntegrationResponse(
                            success=False, message="Schema definition must be valid JSON"
                        )

                # Update is_active if explicitly set
                if hasattr(request, "is_active"):
                    integration.is_active = request.is_active

                integration.updated_at = datetime.now(timezone.utc)
                db.commit()

                # Convert to proto message
                integration_proto = self._integration_to_proto(integration)

                logger.info(f"Updated integration: {integration.name} (ID: {integration.id})")

                return authentication_pb2.UpdateIntegrationResponse(
                    success=True,
                    message="Integration updated successfully",
                    integration=integration_proto,
                )

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error updating integration: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.UpdateIntegrationResponse(
                success=False, message="Internal server error"
            )

    def DeleteIntegration(
        self, request: authentication_pb2.DeleteIntegrationRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.DeleteIntegrationResponse:
        """Delete an integration definition."""
        try:

            if not request.admin_user_id:
                return authentication_pb2.DeleteIntegrationResponse(
                    success=False, message="Admin user ID is required"
                )

            if not request.integration_id:
                return authentication_pb2.DeleteIntegrationResponse(
                    success=False, message="Integration ID is required"
                )

            db = self._get_db_session()
            try:
                # Find existing integration
                integration = (
                    db.query(IntegrationDefinition)
                    .filter(IntegrationDefinition.id == request.integration_id)
                    .first()
                )

                if not integration:
                    return authentication_pb2.DeleteIntegrationResponse(
                        success=False, message="Integration not found"
                    )

                # Check if there are any user credentials associated with this integration
                user_credentials_count = (
                    db.query(OAuthCredential)
                    .filter(OAuthCredential.integration_definition_id == request.integration_id)
                    .count()
                )

                if user_credentials_count > 0:
                    return authentication_pb2.DeleteIntegrationResponse(
                        success=False,
                        message=f"Cannot delete integration. {user_credentials_count} user(s) have credentials for this integration. Please remove user credentials first.",
                    )

                # Delete the integration
                integration_name = integration.name
                db.delete(integration)
                db.commit()

                logger.info(
                    f"Deleted integration: {integration_name} (ID: {request.integration_id})"
                )

                return authentication_pb2.DeleteIntegrationResponse(
                    success=True, message="Integration deleted successfully"
                )

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error deleting integration: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.DeleteIntegrationResponse(
                success=False, message="Internal server error"
            )

    def ListIntegrations(
        self, request: authentication_pb2.ListIntegrationsRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.ListIntegrationsResponse:
        """List all integration definitions with pagination support."""
        try:
            logger.info(
                f"List Integrations Request: page={request.page}, page_size={request.page_size}, include_inactive={request.include_inactive}"
            )

            db = self._get_db_session()
            try:
                # Test database connection first
                try:
                    db.execute(text("SELECT 1"))
                    logger.info("Database connection test successful")
                except Exception as db_test_error:
                    logger.error(f"Database connection test failed: {db_test_error}")
                    raise

                # Build base query
                query = db.query(IntegrationDefinition)
                logger.info(f"Base query created for IntegrationDefinition table")

                # Test if table exists and has data
                try:
                    test_count = db.query(IntegrationDefinition).count()
                    logger.info(f"Total integrations in database (before filters): {test_count}")
                except Exception as table_error:
                    logger.error(f"Error accessing IntegrationDefinition table: {table_error}")
                    raise

                # Filter by connection type if specified
                if request.connection_type_filter != authentication_pb2.CONNECTION_TYPE_UNSPECIFIED:
                    connection_type_map = {
                        authentication_pb2.CONNECTION_TYPE_API_KEY: ConnectionTypeEnum.API_KEY,
                        authentication_pb2.CONNECTION_TYPE_OAUTH: ConnectionTypeEnum.OAUTH,
                    }
                    connection_type = connection_type_map.get(request.connection_type_filter)
                    if connection_type:
                        query = query.filter(
                            IntegrationDefinition.connection_type == connection_type
                        )
                        logger.info(f"Applied connection_type filter: {connection_type}")

                # Filter by provider if specified
                if request.provider and request.provider.strip():
                    query = query.filter(
                        IntegrationDefinition.schema_definition["provider"].astext
                        == request.provider.strip()
                    )
                    logger.info(f"Applied provider filter: {request.provider}")

                # Filter by integration type if specified
                if request.integration_type and request.integration_type.strip():
                    query = query.filter(
                        IntegrationDefinition.schema_definition["integration_type"].astext
                        == request.integration_type.strip()
                    )
                    logger.info(f"Applied integration_type filter: {request.integration_type}")

                # Filter by enabled status if specified
                if hasattr(request, "is_enabled") and request.is_enabled is not None:
                    query = query.filter(IntegrationDefinition.is_active == request.is_enabled)
                    logger.info(f"Applied is_enabled filter: {request.is_enabled}")

                # Always filter to active integrations unless explicitly requested otherwise
                if not request.include_inactive:
                    query = query.filter(IntegrationDefinition.is_active == True)
                    logger.info("Applied active integrations filter")

                # Get total count before pagination
                total_count = query.count()
                logger.info(f"Total integrations found: {total_count}")

                # Apply pagination
                page = max(1, request.page) if request.page > 0 else 1
                page_size = min(max(1, request.page_size), 100) if request.page_size > 0 else 10
                offset = (page - 1) * page_size
                logger.info(f"Pagination: page={page}, page_size={page_size}, offset={offset}")

                # Order by name and apply pagination
                integrations = (
                    query.order_by(IntegrationDefinition.name).offset(offset).limit(page_size).all()
                )
                logger.info(f"Retrieved {len(integrations)} integrations from database")

                # Convert to proto messages
                integration_protos = [
                    self._integration_to_proto(integration) for integration in integrations
                ]
                logger.info(f"Converted {len(integration_protos)} integrations to proto messages")

                return authentication_pb2.ListIntegrationsResponse(
                    success=True,
                    message=f"Retrieved {len(integration_protos)} integration(s) (page {page} of {(total_count + page_size - 1) // page_size})",
                    integrations=integration_protos,
                    total=total_count,
                    page=page,
                    page_size=page_size,
                )

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error listing integrations: {e}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.ListIntegrationsResponse(
                success=False,
                message="Internal server error",
                integrations=[],
                total=0,
                page=1,
                page_size=10,
            )

    def GetIntegration(
        self, request: authentication_pb2.GetIntegrationRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.GetIntegrationResponse:
        """Get a specific integration definition."""
        try:
            if not request.integration_id:
                return authentication_pb2.GetIntegrationResponse(
                    success=False, message="Integration ID is required"
                )

            db = self._get_db_session()
            try:
                integration = (
                    db.query(IntegrationDefinition)
                    .filter(
                        IntegrationDefinition.id == request.integration_id,
                        IntegrationDefinition.is_active == True,
                    )
                    .first()
                )

                if not integration:
                    return authentication_pb2.GetIntegrationResponse(
                        success=False, message="Integration not found"
                    )

                # Convert to proto message
                integration_proto = self._integration_to_proto(integration)

                return authentication_pb2.GetIntegrationResponse(
                    success=True,
                    message="Integration retrieved successfully",
                    integration=integration_proto,
                )

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error getting integration: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.GetIntegrationResponse(
                success=False, message="Internal server error"
            )

    def _integration_to_proto(
        self, integration: IntegrationDefinition
    ) -> authentication_pb2.IntegrationDefinition:
        """Convert IntegrationDefinition model to proto message."""
        # Map connection type to proto enum
        connection_type_map = {
            ConnectionTypeEnum.API_KEY: authentication_pb2.CONNECTION_TYPE_API_KEY,
            ConnectionTypeEnum.OAUTH: authentication_pb2.CONNECTION_TYPE_OAUTH,
        }
        connection_type = connection_type_map.get(
            integration.connection_type, authentication_pb2.CONNECTION_TYPE_UNSPECIFIED
        )

        return authentication_pb2.IntegrationDefinition(
            id=integration.id,
            logo=integration.logo or "",
            name=integration.name,
            description=integration.description or "",
            connection_type=connection_type,
            schema_definition=json.dumps(integration.schema_definition),
            is_active=integration.is_active,
            created_at=integration.created_at.isoformat() if integration.created_at else "",
            updated_at=integration.updated_at.isoformat() if integration.updated_at else "",
        )
