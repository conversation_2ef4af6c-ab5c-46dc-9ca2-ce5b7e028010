"""
OAuth Provider Management

This module defines OAuth provider configurations and management
for the authentication service.
"""

import json
import logging
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class ZohoDataCenter(str, Enum):
    """Zoho data center regions."""

    US = "us"
    EU = "eu"
    IN = "in"
    AU = "au"
    JP = "jp"
    CA = "ca"
    SA = "sa"
    UK = "uk"


class ZohoMultiDCConfig:
    """Configuration for Zoho Multi-DC support."""

    # Data center to server URL mapping
    DC_ENDPOINTS = {
        ZohoDataCenter.US: "https://accounts.zoho.com",
        ZohoDataCenter.EU: "https://accounts.zoho.eu",
        ZohoDataCenter.IN: "https://accounts.zoho.in",
        ZohoDataCenter.AU: "https://accounts.zoho.com.au",
        ZohoDataCenter.JP: "https://accounts.zoho.jp",
        ZohoDataCenter.CA: "https://accounts.zohocloud.ca",
        ZohoDataCenter.SA: "https://accounts.zoho.sa",
        ZohoDataCenter.UK: "https://accounts.zoho.uk",
    }

    # Data center to API domain mapping
    API_DOMAINS = {
        ZohoDataCenter.US: "https://www.zohoapis.com",
        ZohoDataCenter.EU: "https://www.zohoapis.eu",
        ZohoDataCenter.IN: "https://www.zohoapis.in",
        ZohoDataCenter.AU: "https://www.zohoapis.com.au",
        ZohoDataCenter.JP: "https://www.zohoapis.jp",
        ZohoDataCenter.CA: "https://www.zohoapis.ca",
        ZohoDataCenter.SA: "https://www.zohoapis.sa",
        ZohoDataCenter.UK: "https://www.zohoapis.uk",
    }

    @classmethod
    def get_auth_url(cls, dc: ZohoDataCenter) -> str:
        """Get authorization URL for data center."""
        return f"{cls.DC_ENDPOINTS[dc]}/oauth/v2/auth"

    @classmethod
    def get_token_url(cls, dc: ZohoDataCenter) -> str:
        """Get token URL for data center."""
        return f"{cls.DC_ENDPOINTS[dc]}/oauth/v2/token"

    @classmethod
    def get_user_info_url(cls, dc: ZohoDataCenter) -> str:
        """Get user info URL for data center."""
        return f"{cls.API_DOMAINS[dc]}/crm/v6/users?type=CurrentUser"

    @classmethod
    def get_revoke_url(cls, dc: ZohoDataCenter) -> str:
        """Get token revoke URL for data center."""
        return f"{cls.DC_ENDPOINTS[dc]}/oauth/v2/token/revoke"

    @classmethod
    def detect_dc_from_location(cls, location: str) -> ZohoDataCenter:
        """Detect data center from location parameter."""
        location_lower = location.lower()
        try:
            return ZohoDataCenter(location_lower)
        except ValueError:
            logger.warning(f"Unknown Zoho data center location: {location}, defaulting to US")
            return ZohoDataCenter.US


class OAuthProvider(str, Enum):
    """Enumeration of supported OAuth providers."""

    GOOGLE = "google"
    MICROSOFT = "microsoft"
    GITHUB = "github"
    SLACK = "slack"
    CUSTOM = "custom"
    JIRA = "jira"
    ZOHO = "zoho"


class OAuthProviderConfig(BaseModel):
    """Configuration for an OAuth provider."""

    provider: OAuthProvider
    client_id: str
    client_secret: str
    redirect_uri: str
    auth_url: str
    token_url: str
    user_info_url: Optional[str] = None
    revoke_url: Optional[str] = None

    # Provider-specific parameters
    access_type: Optional[str] = None  # For Google: "offline"
    prompt: Optional[str] = None  # For Google: "consent"
    response_type: str = "code"

    # Additional parameters for the authorization URL
    extra_auth_params: Dict[str, str] = Field(default_factory=dict)


class ToolScopeMapping(BaseModel):
    """Mapping of tools to their required scopes for different providers."""

    tool_name: str
    provider_scopes: Dict[OAuthProvider, List[str]]
    description: Optional[str] = None


class OAuthProviderManager:
    """Manages OAuth provider configurations and tool scope mappings."""

    def __init__(self):
        self._providers: Dict[OAuthProvider, OAuthProviderConfig] = {}
        self._tool_scopes: Dict[str, ToolScopeMapping] = {}
        self._initialize_default_providers()
        self._initialize_default_tool_scopes()

    def _initialize_default_providers(self):
        """Initialize default provider configurations."""

        # Google OAuth configuration
        self._providers[OAuthProvider.GOOGLE] = OAuthProviderConfig(
            provider=OAuthProvider.GOOGLE,
            client_id="",  # Will be set from environment
            client_secret="",  # Will be set from environment
            redirect_uri="",  # Will be set from environment
            auth_url="https://accounts.google.com/o/oauth2/auth",
            token_url="https://oauth2.googleapis.com/token",
            user_info_url="https://www.googleapis.com/oauth2/v2/userinfo",
            revoke_url="https://oauth2.googleapis.com/revoke",
            access_type="offline",
            prompt="consent",
            extra_auth_params={},
        )

        # Microsoft OAuth configuration
        self._providers[OAuthProvider.MICROSOFT] = OAuthProviderConfig(
            provider=OAuthProvider.MICROSOFT,
            client_id="",  # Will be set from environment
            client_secret="",  # Will be set from environment
            redirect_uri="",  # Will be set from environment
            auth_url="https://login.microsoftonline.com/common/oauth2/v2.0/authorize",
            token_url="https://login.microsoftonline.com/common/oauth2/v2.0/token",
            user_info_url="https://graph.microsoft.com/v1.0/me",
            revoke_url=None,
            extra_auth_params={},
        )

        # GitHub OAuth configuration
        self._providers[OAuthProvider.GITHUB] = OAuthProviderConfig(
            provider=OAuthProvider.GITHUB,
            client_id="",  # Will be set from environment
            client_secret="",  # Will be set from environment
            redirect_uri="",  # Will be set from environment
            auth_url="https://github.com/login/oauth/authorize",
            token_url="https://github.com/login/oauth/access_token",
            user_info_url="https://api.github.com/user",
            revoke_url=None,
            extra_auth_params={},
        )

        # Slack OAuth configuration
        self._providers[OAuthProvider.SLACK] = OAuthProviderConfig(
            provider=OAuthProvider.SLACK,
            client_id="",  # Will be set from environment
            client_secret="",  # Will be set from environment
            redirect_uri="",  # Will be set from environment
            auth_url="https://slack.com/oauth/v2/authorize",
            token_url="https://slack.com/api/oauth.v2.access",
            user_info_url="https://slack.com/api/users.info",
            revoke_url="https://slack.com/api/auth.revoke",
            extra_auth_params={},
        )

        # Zoho OAuth configuration (Multi-DC support)
        # Note: URLs will be dynamically determined based on user's data center
        self._providers[OAuthProvider.ZOHO] = OAuthProviderConfig(
            provider=OAuthProvider.ZOHO,
            client_id="",  # Will be set from environment
            client_secret="",  # Will be set from environment
            redirect_uri="",  # Will be set from environment
            auth_url="https://accounts.zoho.com/oauth/v2/auth",  # Default to US, will be dynamic
            token_url="https://accounts.zoho.com/oauth/v2/token",  # Default to US, will be dynamic
            user_info_url="https://www.zohoapis.com/crm/v6/users?type=CurrentUser",  # Will be dynamic
            revoke_url="https://accounts.zoho.com/oauth/v2/token/revoke",  # Will be dynamic
            extra_auth_params={
                "access_type": "offline",
                "prompt": "consent",
            },  # Enable Multi-DC support
        )

        # Jira OAuth configuration
        self._providers[OAuthProvider.JIRA] = OAuthProviderConfig(
            provider=OAuthProvider.JIRA,
            client_id="",  # Will be set from environment
            client_secret="",  # Will be set from environment
            redirect_uri="",  # Will be set from environment
            auth_url="https://auth.atlassian.com/authorize",
            token_url="https://auth.atlassian.com/oauth/token",
            user_info_url="https://api.atlassian.com/me",
            revoke_url=None,
            extra_auth_params={
                "audience": "api.atlassian.com",
            },
        )

    def _initialize_default_tool_scopes(self):
        """Initialize default tool scope mappings."""

        # Google Calendar tool scopes
        self._tool_scopes["google_calendar"] = ToolScopeMapping(
            tool_name="google_calendar",
            provider_scopes={
                OAuthProvider.GOOGLE: [
                    "https://www.googleapis.com/auth/calendar",
                    "https://www.googleapis.com/auth/calendar.events",
                ]
            },
            description="Google Calendar access for event management",
        )

        # Google Drive tool scopes
        self._tool_scopes["google_drive"] = ToolScopeMapping(
            tool_name="google_drive",
            provider_scopes={
                OAuthProvider.GOOGLE: [
                    "https://www.googleapis.com/auth/drive",
                    "https://www.googleapis.com/auth/drive.file",
                ]
            },
            description="Google Drive access for file management",
        )

        # Google Document or Docs tool scopes
        self._tool_scopes["google_document"] = ToolScopeMapping(
            tool_name="google_document",
            provider_scopes={
                OAuthProvider.GOOGLE: [
                    "https://www.googleapis.com/auth/documents",
                ]
            },
            description="Google Document access for document management",
        )

        # Gmail tool scopes
        self._tool_scopes["gmail"] = ToolScopeMapping(
            tool_name="gmail",
            provider_scopes={
                OAuthProvider.GOOGLE: [
                    "https://www.googleapis.com/auth/gmail.modify",
                    "https://www.googleapis.com/auth/gmail.compose",
                    "https://www.googleapis.com/auth/gmail.send",
                    "https://www.googleapis.com/auth/gmail.labels",
                    "https://www.googleapis.com/auth/gmail.readonly",
                ]
            },
            description="Gmail access for email management",
        )

        # Google Sheets tool scopes
        self._tool_scopes["google_sheets"] = ToolScopeMapping(
            tool_name="google_sheets",
            provider_scopes={
                OAuthProvider.GOOGLE: ["https://www.googleapis.com/auth/spreadsheets"]
            },
            description="Google Sheets access for sheet management",
        )

        # Microsoft Graph tool scopes
        self._tool_scopes["microsoft_graph"] = ToolScopeMapping(
            tool_name="microsoft_graph",
            provider_scopes={
                OAuthProvider.MICROSOFT: [
                    "https://graph.microsoft.com/User.Read",
                    "https://graph.microsoft.com/Calendars.ReadWrite",
                    "https://graph.microsoft.com/Mail.ReadWrite",
                ]
            },
            description="Microsoft Graph API access",
        )

        # GitHub tool scopes
        self._tool_scopes["github"] = ToolScopeMapping(
            tool_name="github",
            provider_scopes={OAuthProvider.GITHUB: ["repo", "user:email", "read:org"]},
            description="GitHub API access for repository management",
        )

        # Slack tool scopes with delimiter-based categorization
        self._tool_scopes["slack"] = ToolScopeMapping(
            tool_name="slack",
            provider_scopes={
                OAuthProvider.SLACK: [
                    # Bot scopes (prefixed with bot_)
                    "bot_channels:read",
                    "bot_chat:write",
                    "bot_reactions:write",
                    "bot_channels:history",
                    "bot_users:read",
                    "bot_groups:read",
                    "bot_im:read",
                    "bot_mpim:read",
                    "bot_channels:join",
                    "bot_groups:history",
                    "bot_im:history",
                    "bot_mpim:history",
                    "bot_chat:write.public",
                    "bot_reminders:write",
                    "bot_channels:write.invites",
                    "bot_groups:write.invites",
                    "bot_channels:manage",
                    "bot_groups:write",
                    "bot_im:write",
                    "bot_mpim:write",
                    "bot_channels:write.topic",
                    "bot_groups:write.topic",
                    "bot_mpim:write.topic",
                    "bot_im:write.topic",
                    "bot_users.profile:read",
                    # User scopes (prefixed with user_)
                    "user_search:read",
                    "user_users.profile:write",
                ]
            },
            description="Slack API access with bot and user tokens for messaging and user management",
        )

        # Zoho tool scopes
        self._tool_scopes["zoho"] = ToolScopeMapping(
            tool_name="zoho",
            provider_scopes={
                OAuthProvider.ZOHO: [
                    "ZohoCRM.modules.ALL",
                    "ZohoCRM.users.READ",
                    "ZohoCRM.org.READ",
                    "ZohoCRM.settings.READ",
                ]
            },
            description="ZOHO API access for CRM management",
        )

        # Jira tool scopes
        self._tool_scopes["jira"] = ToolScopeMapping(
            tool_name="jira",
            provider_scopes={
                OAuthProvider.JIRA: [
                    # --- Jira Platform API Scopes ---
                    "read:jira-user",
                    "read:jira-work",
                    "write:jira-work",
                    "manage:jira-project",
                    "manage:jira-configuration",
                    # --- Jira Software API Scopes (Full Management) ---
                    # "read:board-scope:jira-software",  # Read board data and configuration
                    # "write:board-scope:jira-software",  # Create and update boards
                    "read:sprint:jira-software",  # Read sprint information
                    "write:sprint:jira-software",  # Create and update sprints (e.g., change name/dates)
                    "delete:sprint:jira-software",  # Delete sprints
                    "manage:sprint:jira-software",  # Start/stop sprints, move issues between sprints/backlog
                    # # --- Confluence API Scopes ---
                    "read:page:confluence",
                    "read:space:confluence",
                    "write:page:confluence",
                    "read:content.all:confluence",
                    "read:content.permission:confluence",
                    "read:space.content:confluence",
                    "write:content:confluence",
                    "write:content.permission:confluence",
                    "write:space.content:confluence",
                    "search:confluence",
                    "read:comment:confluence",
                    "delete:page:confluence",
                    # --- Standard OAuth 2.0 Scope ---
                    "offline_access",
                ]
            },
            description="Jira API access (read/write issues, user info, offline access)",
        )

    def get_provider_config(self, provider: OAuthProvider) -> Optional[OAuthProviderConfig]:
        """Get configuration for a specific provider."""
        return self._providers.get(provider)

    def update_provider_credentials(
        self, provider: OAuthProvider, client_id: str, client_secret: str, redirect_uri: str
    ):
        """Update provider credentials."""
        if provider in self._providers:
            config = self._providers[provider]
            config.client_id = client_id
            config.client_secret = client_secret
            config.redirect_uri = redirect_uri
            logger.info(f"Updated credentials for provider: {provider.value}")
        else:
            logger.warning(f"Provider not found: {provider.value}")

    def get_tool_scopes(self, tool_name: str, provider: OAuthProvider) -> List[str]:
        """Get required scopes for a tool and provider."""
        tool_mapping = self._tool_scopes.get(tool_name)
        if not tool_mapping:
            logger.warning(f"No scope mapping found for tool: {tool_name}")
            return []

        scopes = tool_mapping.provider_scopes.get(provider, [])
        if not scopes:
            logger.warning(f"No scopes found for tool {tool_name} and provider {provider.value}")

        return scopes

    def get_slack_scopes_separated(self, tool_name: str) -> Dict[str, List[str]]:
        """
        Get Slack scopes separated into bot and user categories.

        Args:
            tool_name: Tool name

        Returns:
            Dictionary with 'bot_scopes' and 'user_scopes' keys
        """
        all_scopes = self.get_tool_scopes(tool_name, OAuthProvider.SLACK)

        bot_scopes = []
        user_scopes = []

        for scope in all_scopes:
            if scope.startswith("bot_"):
                # Remove the bot_ prefix for actual Slack API
                bot_scopes.append(scope[4:])  # Remove "bot_" prefix
            elif scope.startswith("user_"):
                # Remove the user_ prefix for actual Slack API
                user_scopes.append(scope[5:])  # Remove "user_" prefix
            else:
                # Default to bot scope if no prefix
                bot_scopes.append(scope)

        return {"bot_scopes": bot_scopes, "user_scopes": user_scopes}

    def is_slack_provider(self, provider: OAuthProvider) -> bool:
        """Check if the provider is Slack."""
        return provider == OAuthProvider.SLACK

    def get_all_providers(self) -> Dict[OAuthProvider, OAuthProviderConfig]:
        """Get all provider configurations."""
        return self._providers.copy()

    def get_all_tool_scopes(self) -> Dict[str, ToolScopeMapping]:
        """Get all tool scope mappings."""
        return self._tool_scopes.copy()

    def is_provider_configured(self, provider: OAuthProvider) -> bool:
        """Check if a provider is properly configured."""
        config = self._providers.get(provider)
        if not config:
            return False

        return bool(config.client_id and config.client_secret and config.redirect_uri)

    def load_custom_providers_from_json(self, json_config: str):
        """
        Load custom provider configurations from JSON string with enhanced validation.

        Args:
            json_config: JSON string containing custom provider configurations

        Expected JSON format:
        {
            "provider_name": {
                "client_id": "...",
                "client_secret": "...",
                "redirect_uri": "...",
                "auth_url": "...",
                "token_url": "...",
                "user_info_url": "...",  // optional
                "revoke_url": "...",     // optional
                "access_type": "...",    // optional
                "prompt": "...",         // optional
                "response_type": "code", // optional, defaults to "code"
                "extra_auth_params": {}  // optional
            }
        }
        """
        try:
            custom_configs = json.loads(json_config)

            if not isinstance(custom_configs, dict):
                raise ValueError("Custom providers configuration must be a JSON object")

            for provider_name, config_data in custom_configs.items():
                # Validate required fields
                required_fields = [
                    "client_id",
                    "client_secret",
                    "redirect_uri",
                    "auth_url",
                    "token_url",
                ]
                for field in required_fields:
                    if field not in config_data:
                        raise ValueError(
                            f"Missing required field '{field}' for provider '{provider_name}'"
                        )

                # Validate URLs
                for url_field in ["auth_url", "token_url", "redirect_uri"]:
                    url = config_data[url_field]
                    if not url.startswith(("http://", "https://")):
                        raise ValueError(
                            f"Invalid URL for '{url_field}' in provider '{provider_name}': {url}"
                        )

                # Create custom provider config with validation
                custom_config = OAuthProviderConfig(
                    provider=OAuthProvider.CUSTOM,
                    client_id=config_data["client_id"].strip(),
                    client_secret=config_data["client_secret"].strip(),
                    redirect_uri=config_data["redirect_uri"].strip(),
                    auth_url=config_data["auth_url"].strip(),
                    token_url=config_data["token_url"].strip(),
                    user_info_url=config_data.get("user_info_url", "").strip() or None,
                    revoke_url=config_data.get("revoke_url", "").strip() or None,
                    access_type=config_data.get("access_type", "").strip() or None,
                    prompt=config_data.get("prompt", "").strip() or None,
                    response_type=config_data.get("response_type", "code").strip(),
                    extra_auth_params=config_data.get("extra_auth_params", {}),
                )

                # Store with custom name using a consistent key format
                custom_key = f"custom_{provider_name.lower().replace(' ', '_')}"
                self._providers[custom_key] = custom_config
                logger.info(f"Loaded custom OAuth provider: {provider_name} (key: {custom_key})")

        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in custom OAuth providers configuration: {e}")
            raise ValueError(f"Invalid JSON configuration: {e}")
        except (KeyError, ValueError) as e:
            logger.error(f"Configuration error in custom OAuth providers: {e}")
            raise ValueError(f"Configuration error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error loading custom OAuth providers: {e}")
            raise RuntimeError(f"Failed to load custom providers: {e}")

    def add_tool_scope_mapping(self, tool_mapping: ToolScopeMapping):
        """Add a new tool scope mapping."""
        self._tool_scopes[tool_mapping.tool_name] = tool_mapping
        logger.info(f"Added tool scope mapping for: {tool_mapping.tool_name}")

    def get_provider_by_name(self, provider_name: str) -> Optional[OAuthProviderConfig]:
        """
        Get provider configuration by name (supports both standard and custom providers).

        Args:
            provider_name: Provider name (e.g., "google", "microsoft", "custom_mycompany")

        Returns:
            Provider configuration if found, None otherwise
        """
        # Try direct lookup first
        if provider_name in self._providers:
            return self._providers[provider_name]

        # Try with custom prefix
        custom_key = f"custom_{provider_name.lower().replace(' ', '_')}"
        if custom_key in self._providers:
            return self._providers[custom_key]

        # Try enum-based lookup for standard providers
        try:
            provider_enum = OAuthProvider(provider_name.lower())
            return self._providers.get(provider_enum)
        except ValueError:
            pass

        return None

    def list_available_providers(self) -> Dict[str, Dict[str, Any]]:
        """
        List all available OAuth providers with their configuration status.

        Returns:
            Dictionary mapping provider names to their status and capabilities
        """
        providers_info = {}

        for key, config in self._providers.items():
            # Handle both enum keys and string keys
            if isinstance(key, OAuthProvider):
                provider_key = key.value
                is_configured = self.is_provider_configured(key)
            else:
                provider_key = str(key)
                is_configured = bool(
                    config.client_id and config.client_secret and config.redirect_uri
                )

            # Get supported tools for this provider
            supported_tools = []
            for tool_name, mapping in self._tool_scopes.items():
                if config.provider in mapping.provider_scopes:
                    supported_tools.append(tool_name)

            providers_info[provider_key] = {
                "provider_type": config.provider.value,
                "display_name": provider_key.replace("_", " ").title(),
                "is_configured": is_configured,
                "supported_tools": supported_tools,
                "has_user_info_url": config.user_info_url is not None,
                "has_revoke_url": config.revoke_url is not None,
                "response_type": config.response_type,
                "extra_params": list(config.extra_auth_params.keys()),
            }

        return providers_info

    def validate_provider_configuration(self, provider: OAuthProvider) -> List[str]:
        """
        Validate provider configuration and return list of issues.

        Args:
            provider: OAuth provider to validate

        Returns:
            List of validation error messages (empty if valid)
        """
        issues = []
        config = self._providers.get(provider)

        if not config:
            issues.append(f"Provider {provider.value} is not configured")
            return issues

        # Check required fields
        if not config.client_id:
            issues.append("Client ID is missing")
        if not config.client_secret:
            issues.append("Client secret is missing")
        if not config.redirect_uri:
            issues.append("Redirect URI is missing")
        if not config.auth_url:
            issues.append("Authorization URL is missing")
        if not config.token_url:
            issues.append("Token URL is missing")

        # Validate URLs
        for url_field, url_value in [
            ("auth_url", config.auth_url),
            ("token_url", config.token_url),
            ("redirect_uri", config.redirect_uri),
            ("user_info_url", config.user_info_url),
            ("revoke_url", config.revoke_url),
        ]:
            if url_value and not url_value.startswith(("http://", "https://")):
                issues.append(f"Invalid {url_field}: {url_value}")

        return issues


# Global provider manager instance
oauth_provider_manager = OAuthProviderManager()
