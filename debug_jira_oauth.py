#!/usr/bin/env python3
"""
Debug script for Jira OAuth issues.

This script helps debug the Jira OAuth flow, particularly issues with:
1. State parameter handling
2. Multiple app access vs single app access
3. Scope configuration issues
"""

import os
import sys
import json
from urllib.parse import urlencode, parse_qs, urlparse

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.core.oauth_providers import oauth_provider_manager, OAuthProvider


def debug_jira_oauth_config():
    """Debug the current Jira OAuth configuration."""
    print("=== Jira OAuth Configuration Debug ===\n")
    
    # Get Jira provider config
    jira_config = oauth_provider_manager.get_provider_config(OAuthProvider.JIRA)
    
    if not jira_config:
        print("❌ ERROR: Jira provider not configured!")
        return
    
    print("✅ Jira Provider Configuration:")
    print(f"   Auth URL: {jira_config.auth_url}")
    print(f"   Token URL: {jira_config.token_url}")
    print(f"   User Info URL: {jira_config.user_info_url}")
    print(f"   Access Type: {jira_config.access_type}")
    print(f"   Prompt: {jira_config.prompt}")
    print(f"   Extra Auth Params: {jira_config.extra_auth_params}")
    print()
    
    # Check if provider is properly configured
    validation_issues = oauth_provider_manager.validate_provider_configuration(OAuthProvider.JIRA)
    if validation_issues:
        print("❌ Configuration Issues:")
        for issue in validation_issues:
            print(f"   - {issue}")
    else:
        print("✅ Configuration is valid")
    print()
    
    # Check scopes
    print("📋 Available Scope Configurations:")
    
    # Essential scopes for multiple apps
    essential_scopes = oauth_provider_manager.get_jira_scopes_for_multiple_apps()
    print(f"   Essential (Multiple Apps): {len(essential_scopes)} scopes")
    for scope in essential_scopes:
        print(f"     - {scope}")
    print()
    
    # Extended scopes for single app
    extended_scopes = oauth_provider_manager.get_jira_scopes_for_single_app()
    print(f"   Extended (Single App): {len(extended_scopes)} scopes")
    for scope in extended_scopes:
        print(f"     - {scope}")
    print()


def generate_test_auth_url(use_extended_scopes=False):
    """Generate a test authorization URL to debug the OAuth flow."""
    print("=== Test Authorization URL Generation ===\n")
    
    # Mock configuration (you should replace with actual values)
    client_id = os.getenv('JIRA_CLIENT_ID', 'your-client-id')
    redirect_uri = os.getenv('JIRA_REDIRECT_URI', 'http://localhost:8000/api/v1/oauth/callback')
    
    if client_id == 'your-client-id':
        print("⚠️  WARNING: Using placeholder client_id. Set JIRA_CLIENT_ID environment variable.")
    
    # Get configuration
    jira_config = oauth_provider_manager.get_provider_config(OAuthProvider.JIRA)
    
    # Choose scopes
    if use_extended_scopes:
        scopes = oauth_provider_manager.get_jira_scopes_for_single_app()
        scope_type = "Extended (Single App)"
    else:
        scopes = oauth_provider_manager.get_jira_scopes_for_multiple_apps()
        scope_type = "Essential (Multiple Apps)"
    
    # Generate test state token
    import secrets
    state_token = secrets.token_urlsafe(32)
    
    # Build auth parameters
    auth_params = {
        "client_id": client_id,
        "redirect_uri": redirect_uri,
        "scope": " ".join(scopes),
        "state": state_token,
        "response_type": "code",
    }
    
    # Add provider-specific parameters
    if jira_config.access_type:
        auth_params["access_type"] = jira_config.access_type
    
    if jira_config.prompt:
        auth_params["prompt"] = jira_config.prompt
    
    # Add extra parameters
    auth_params.update(jira_config.extra_auth_params)
    
    # Build URL
    auth_url = f"{jira_config.auth_url}?{urlencode(auth_params)}"
    
    print(f"🔗 Generated Authorization URL ({scope_type}):")
    print(f"   {auth_url}")
    print()
    
    print("📋 URL Parameters:")
    parsed_url = urlparse(auth_url)
    params = parse_qs(parsed_url.query)
    for key, values in params.items():
        print(f"   {key}: {values[0] if len(values) == 1 else values}")
    print()
    
    print(f"🔑 State Token: {state_token}")
    print()
    
    return auth_url, state_token


def analyze_problematic_url(url):
    """Analyze the problematic URL you provided."""
    print("=== Analyzing Problematic URL ===\n")
    
    print(f"🔍 URL: {url}")
    print()
    
    parsed_url = urlparse(url)
    params = parse_qs(parsed_url.query)
    
    print("📋 URL Parameters:")
    for key, values in params.items():
        value = values[0] if len(values) == 1 else values
        print(f"   {key}: {value}")
        
        if key == "state" and value == "undefined":
            print("   ❌ ISSUE: State parameter is 'undefined'!")
    print()
    
    # Check if this is the consent URL (which indicates the OAuth flow started)
    if "oauth2/authorize/server/consent/grant" in url:
        print("ℹ️  This is an Atlassian consent URL, which means:")
        print("   - The initial OAuth request was successful")
        print("   - Atlassian is asking for user consent")
        print("   - The issue is likely with state parameter handling")
        print()


def main():
    """Main debug function."""
    print("🔧 Jira OAuth Debug Tool\n")
    
    # Debug configuration
    debug_jira_oauth_config()
    
    # Generate test URLs
    print("🧪 Generating test authorization URLs...\n")
    
    # Essential scopes (for multiple apps)
    print("1. Essential Scopes (Multiple Apps Compatible):")
    generate_test_auth_url(use_extended_scopes=False)
    
    # Extended scopes (for single app)
    print("2. Extended Scopes (Single App):")
    generate_test_auth_url(use_extended_scopes=True)
    
    # Analyze the problematic URL
    problematic_url = "https://api.atlassian.com/oauth2/authorize/server/consent/grant?state=undefined&auth0Url=https://auth.atlassian.com/authorize&context=eyJraWQi..."
    analyze_problematic_url(problematic_url)
    
    print("💡 Recommendations:")
    print("   1. Use the 'Essential Scopes' configuration for multiple app compatibility")
    print("   2. Ensure the frontend/client properly passes the state parameter")
    print("   3. Check that the state parameter is not being overwritten")
    print("   4. Verify that the OAuth flow uses the correct redirect URI")
    print("   5. Test with a single app first, then multiple apps")


if __name__ == "__main__":
    main()
